"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import JC_PasswordRequirements from "../components/JC_PasswordRequirements/JC_PasswordRequirements";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Post } from "../apiServices/JC_Post";
import { signIn } from "next-auth/react";
import { UserModel } from "../models/User";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils, JC_Utils_Validation } from "../Utils";

export default function Page_Register() {

    const router = useRouter();

    // - STATE - //

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>("");
    const [submitClicked, setSubmitClicked] = useState<boolean>(false);
    const [registerFirstName, setRegisterFirstName] = useState<string>("");
    const [registerLastName, setRegisterLastName] = useState<string>("");
    const [registerEmail, setRegisterEmail] = useState<string>("");
    const [registerPhone, setRegisterPhone] = useState<string>();
    const [registerCompany, setRegisterCompany] = useState<string>("");
    const [registerPassword, setRegisterPassword] = useState<string>("");
    const [registerConfirmPassword, setRegisterConfirmPassword] = useState<string>("");
    const [isAdminChecked, setIsAdminChecked] = useState<boolean>(false);

    // - EFFECTS - //

    useEffect(() => {
        if (!JC_Utils.isOnMobile()) {
            setTimeout(() => (document.getElementById("register-first-input") as HTMLInputElement)?.select(), 0);
        }
    }, []);

    // - HANDLES - //

    async function register() {
        setSubmitClicked(true);
        setIsLoading(true);
        setErrorMessage("");
        try {
            let newUser:UserModel = new UserModel({
                FirstName: registerFirstName,
                LastName: registerLastName,
                Email: registerEmail,
                Phone: registerPhone,
                CompanyName: !JC_Utils.stringNullOrEmpty(registerCompany) ? registerCompany : undefined,
                IsAdmin: isAdminChecked
            });
            // Create User
            await JC_Put<{ userData:UserModel, password:string }>(
                UserModel.apiRoute,
                { userData: newUser, password: registerPassword }
            );
            // Send 'Welcome' email
            JC_Post("email/welcomeEmail", { name: `${newUser.FirstName} ${newUser.LastName}`, email: newUser.Email });
            // Login, then go back "Home"
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
            await signIn("credentials", { email: registerEmail, password: registerPassword, callbackUrl: "/" });
        } catch (error) {
            setErrorMessage((error as {message:string}).message);
            setIsLoading(false);
        }
    }

    // - Main - //

    return (
        <div className={styles.mainContainer}>
            <div className={styles.formContainer}>

                {/* Title */}
                <JC_Title title="Register" />

                {/* Form */}
                <JC_Form
                    key={errorMessage}
                    submitButtonText="Register"
                    onSubmit={register}
                    isLoading={isLoading}
                    errorMessage={errorMessage}
                    fields={[
                        // First Name
                        {
                            ...D_FieldModel_FirstName(),
                            inputId:"register-first-input",
                            onChange: (newValue) => setRegisterFirstName(newValue),
                            value: registerFirstName
                        },
                        // Last Name
                        {
                            ...D_FieldModel_LastName(),
                            inputId:"register-last-name-input",
                            onChange: (newValue) => setRegisterLastName(newValue),
                            value: registerLastName
                        },
                        // Email
                        {
                            ...D_FieldModel_Email(),
                            inputId:"register-email-input",
                            onChange: (newValue) => setRegisterEmail(newValue),
                            value: registerEmail
                        },
                        // Phone
                        {
                            ...D_FieldModel_Phone(),
                            inputId:"register-phone-input",
                            onChange: (newValue) => setRegisterPhone(newValue),
                            value: registerPhone,
                        },
                        // Company Name
                        {
                            inputId: "register-company-input",
                            type: FieldTypeEnum.Text,
                            label: "Company (optional)",
                            iconName: "User",
                            value: registerCompany,
                            onChange: (newValue) => setRegisterCompany(newValue)
                        },
                        // Password
                        {
                            inputId:"register-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Password",
                            onChange: (newValue) => setRegisterPassword(newValue),
                            value: registerPassword,
                            validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                    ? "Enter a password."
                                                    : !JC_Utils_Validation.validPassword(v)
                                                        ? `Password invalid.`
                                                        : ""
                        },
                        // Password Requirements
                        {
                            overrideClass: styles.passwordRequirementsField,
                            inputId: "password-requirements",
                            type: FieldTypeEnum.Custom,
                            customNode: <JC_PasswordRequirements
                                            key="password-requirements"
                                            password={registerPassword}
                                            showErrors={submitClicked}
                                        />
                        },
                        // Confirm Password
                        {
                            inputId:"register-confirm-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Confirm Password",
                            onChange: (newValue) => setRegisterConfirmPassword(newValue),
                            value: registerConfirmPassword,
                            validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                    ? "Confirm the password."
                                                    : registerConfirmPassword != registerPassword
                                                        ? "Passwords do not match"
                                                        : ""
                        },
                        // Admin User
                        {
                            inputId: "admin-user-checkbox",
                            type: FieldTypeEnum.Custom,
                            customNode: <JC_Checkbox key="admin-user-checkbox" label="Admin User" checked={isAdminChecked} onChange={() => setIsAdminChecked(!isAdminChecked)} />
                        }
                    ]}
                />

            </div>
        </div>
    );
}
