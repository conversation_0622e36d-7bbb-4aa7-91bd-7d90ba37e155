
// - User - //

Table User {
  Id                      UUID         [not null, PK]
  FirstName               varchar(100) [not null]
  LastName                varchar(100) [    null]
  Email                   varchar(100) [not null]
  PasswordHash            varchar      [not null]
  LoginFailedAttempts     int          [not null, default: 0]
  LoginLockoutDate        timestamp    [    null]
  ChangePasswordToken     varchar(200) [    null]
  ChangePasswordTokenDate timestamp    [    null]
  Phone                   varchar(20)  [    null]
  IsAdmin                 boolean      [not null, default: FALSE, note: 'An admin user can make a user admin through Users list page.']
  IsWholesale             boolean      [not null, default: FALSE, note: 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.']
  CompanyName             varchar(200) [    null,                 note: 'Required field for wholesale users.']
  Qualification           varchar(200) [    null,                 note: 'Just free text.']
  IsEmailSubscribed       boolean      [not null, default: TRUE]
  IsDiscountUser          boolean      [not null, default: FALSE, note: 'Discount amount set in [GlobalSettings].']
  StripeCustomerId        varchar(100) [    null,                 note: 'Set when user makes payment for first time since their Stripe customer account is created.']
  IsVerified              boolean      [not null, default: FALSE]
  VerificationToken       varchar(200) [    null]
  CreatedAt               timestamp    [not null, default: 'now()']
  ModifiedAt              timestamp    [    null]
  Deleted                 boolean      [not null, default: FALSE]
}

Table UserPersistedData {
    Id     UUID         [not null, PK]
    UserId UUID         [not null, ref: > User.Id]
    Code   varchar(50)  [not null, PK]
    Value  varchar(200) [not null]
}


// - Property - //

Table Property {
    Id                           UUID         [not null, PK]
    Address                      varchar(200) [not null]
    BuildingTypeCode             varchar(50)  [    null, ref: > O_BuildingType.Code]
    CompanyStrataTitleCode       varchar(50)  [    null, ref: > O_CompanyStrataTitle.Code]
    NumBedroomsCode              varchar(50)  [    null, ref: > O_NumBedrooms.Code]
    OrientationCode              varchar(50)  [    null, ref: > O_Orientation.Code]
    StoreysCode                  varchar(50)  [    null, ref: > O_Storeys.Code]
    FurnishedCode                varchar(50)  [    null, ref: > O_Furnished.Code]
    OccupiedCode                 varchar(50)  [    null, ref: > O_Occupied.Code]
    FloorCode                    varchar(50)  [    null, ref: > O_Floor.Code]
    OtherBuildingElementsCode    varchar(50)  [    null, ref: > O_OtherBuildingElements.Code]
    OtherTimberBldgElementsCode  varchar(50)  [    null, ref: > O_OtherTimberBldgElements.Code]
    RoofCode                     varchar(50)  [    null, ref: > O_Roof.Code]
    WallsCode                    varchar(50)  [    null, ref: > O_Walls.Code]
    WeatherCode                  varchar(50)  [    null, ref: > O_Weather.Code]
    RoomsListJson                varchar      [    null, note: 'JSON array of room codes.']
    SortOrder                    int          [not null, default: 999]
    CreatedAt                    timestamp    [not null, default: 'now()']
    ModifiedAt                   timestamp    [    null]
    Deleted                      boolean      [not null, default: FALSE]
}

Table PropertyDefect {
    Id                 UUID         [not null, PK]
    PropertyId         UUID         [not null, ref: > Property.Id]
    DefectCategoryCode varchar(50)  [not null, ref: > DefectCategory.Code]
    Description        varchar(200) [not null]
    AreaCode           varchar(50)  [not null]
    LocationCode       varchar(50)  [not null]
    OrientationCode    varchar(50)  [not null]
    Defects            varchar(500) [not null, note: 'Just free text.']
    ServerityCode      varchar(50)  [not null]
    CreatedAt          timestamp    [not null, default: 'now()']
    ModifiedAt         timestamp    [    null]
    Deleted            boolean      [not null, default: FALSE]
}

Table DefectImage {
    Id          UUID         [not null, PK]
    DefectId    UUID         [not null, ref: > PropertyDefect.Id]
    ImageName   varchar(200) [not null]
    ImageFileId UUID         [not null]
    CreatedAt   timestamp    [not null, default: 'now()']
    ModifiedAt  timestamp    [    null]
    Deleted     boolean      [not null, default: FALSE]
}

Table Report {
    Id                             UUID         [not null, PK]
    PropertyId                     UUID         [not null, ref: > Property.Id]
    UserId                         UUID         [not null, ref: > User.Id]
    Name                           varchar(200) [not null]
    PostalAddress                  varchar(200) [not null]
    ClientName                     varchar(200) [not null]
    ClientPhone                    varchar(20)  [not null]
    ClientEmail                    varchar(100) [not null]
    ClientPrincipalName            varchar(200) [not null]
    InspectionDate                 timestamp    [not null]
    InspectorNameOverride          varchar(200) [    null]
    InspectorPhoneOverride         varchar(20)  [    null]
    InspectorQualificationOverride varchar(100) [    null]
    FileId                         UUID         [not null]
    CreatedAt                      timestamp    [not null, default: 'now()']
    ModifiedAt                     timestamp    [    null]
    Deleted                        boolean      [not null, default: FALSE]
}


// - Options - //

Table O_BuildingType {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_CompanyStrataTitle {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_NumBedrooms {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Orientation {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Storeys {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Furnished {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Occupied {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Floor {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_OtherBuildingElements {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_OtherTimberBldgElements {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Roof {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Walls {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Weather {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}

Table O_Location {
    Code       varchar(50)  [not null, PK]
    Name       varchar(100) [not null]
    SortOrder  int          [not null, default: 999]
    CreatedAt  timestamp    [not null, default: 'now()']
    ModifiedAt timestamp    [    null]
    Deleted    boolean      [not null, default: FALSE]
}


// - Contact - //

Table Contact {
  Id        UUID         [not null, PK]
  UserId    UUID         [    null, ref: > User.Id, note: 'Nullable since user might not be logged in so just provide name.']
  Name      varchar(200) [not null, note: 'If UserId not null then this will be the User record\'s FirstName+LastName, otherwise is whatever user inputs on "Contact" form.']
  Email     varchar(100) [not null]
  Phone     varchar(20)  [    null]
  Message   varchar      [not null]
  CreatedAt timestamp    [not null, default: 'now()']
}


// - Global Settings - //

Table GlobalSettings {
  Code        varchar(50)  [not null, PK]
  Description varchar(200) [not null]
  Value       varchar(200) [not null]
}