import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";

export class DefectCategoryModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "defectCategory";
    static async Get(code: string) {
        return JC_Get<DefectCategoryModel>(this.apiRoute, { code }, DefectCategoryModel);
    }
    static async GetList() {
        return JC_GetList<DefectCategoryModel>(`${this.apiRoute}/getList`, {}, DefectCategoryModel);
    }
    static async Create(data: DefectCategoryModel) {
        return JC_Put<DefectCategoryModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: DefectCategoryModel[]) {
        return JC_Put<DefectCategoryModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: DefectCategoryModel) {
        return JC_Post<DefectCategoryModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: DefectCategoryModel[]) {
        return JC_Post<DefectCategoryModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<DefectCategoryModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
