@import '../global';

.mainContainer {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Left Pane - Rooms List
    .leftPane {
        width: 250px;
        min-width: 250px;
        display: flex;
        flex-direction: column;

        .leftPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $primaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
        }

        .roomsList {
            flex: 1;
            overflow-y: auto;
            padding: 10px;

            .roomItem {
                padding: 12px 15px;
                margin-bottom: 6px;
                background-color: $offWhite;
                border: 1px solid $lightGrey;
                border-radius: $tinyBorderRadius;
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 14px;

                &:hover {
                    background-color: $grey;
                    border-color: $primaryColor;
                }

                &.selected {
                    background-color: $primaryColor;
                    border-color: $primaryColor;
                    color: $primaryColor;
                    font-weight: bold;
                }
            }

            .noRooms {
                text-align: center;
                color: $darkGrey;
                padding: 40px 20px;
                font-style: italic;
            }
        }
    }

    // Right Pane - Defects List
    .rightPane {
        flex: 1;
        display: flex;
        flex-direction: column;

        .rightPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $secondaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .defectCount {
                font-size: 14px;
                font-weight: normal;
                opacity: 0.9;
            }
        }

        .defectsContainer {
            flex: 1;
            padding: 20px;
            overflow-y: auto;

            .defectsTable {
                width: 100%;

                // Override JC_List styles for better defects display
                :global(.defectsListOverride) {
                    font-size: 13px;

                    th {
                        background-color: $lightGrey;
                        color: $offBlack;
                        font-weight: 600;
                        padding: 12px 8px;
                        border-bottom: 2px solid $primaryColor;
                    }

                    td {
                        padding: 10px 8px;
                        border-bottom: 1px solid $lightGrey;
                        vertical-align: top;

                        &.defectsColumn {
                            max-width: 200px;
                            word-wrap: break-word;
                        }

                        &.imagesColumn {
                            text-align: center;
                            font-weight: bold;
                            color: $secondaryColor;
                        }
                    }

                    tr:hover {
                        background-color: $offWhite;
                    }
                }
            }
        }

        .noDefects {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: $darkGrey;
            font-style: italic;
            font-size: 16px;
        }

        .noRoomSelected {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: $darkGrey;
            font-style: italic;
            font-size: 16px;
        }
    }
}

// Property selector
.propertySelector {
    padding: 15px 20px;
    border-bottom: 1px solid $lightGrey;
    background-color: $offWhite;

    .selectorLabel {
        font-size: 14px;
        font-weight: 500;
        color: $darkGrey;
        margin-bottom: 8px;
    }
}

// Responsive Design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        flex-direction: column;
        height: auto;

        .leftPane {
            width: 100%;
            min-width: auto;
            max-height: 200px;
        }

        .rightPane {
            min-height: 500px;
        }
    }
}
