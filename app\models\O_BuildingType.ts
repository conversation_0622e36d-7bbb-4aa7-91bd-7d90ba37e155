import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";

export class O_BuildingTypeModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "o_buildingType";
    static async Get(code: string) {
        return JC_Get<O_BuildingTypeModel>(this.apiRoute, { code }, O_BuildingTypeModel);
    }
    static async GetList() {
        return JC_GetList<O_BuildingTypeModel>(`${this.apiRoute}/getList`, {}, O_BuildingTypeModel);
    }
    static async Create(data: O_BuildingTypeModel) {
        return JC_Put<O_BuildingTypeModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_BuildingTypeModel[]) {
        return JC_Put<O_BuildingTypeModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_BuildingTypeModel) {
        return JC_Post<O_BuildingTypeModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_BuildingTypeModel[]) {
        return JC_Post<O_BuildingTypeModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_BuildingTypeModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
