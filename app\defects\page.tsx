"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import JC_List from "../components/JC_List/JC_List";
import JC_Dropdown from "../components/JC_Dropdown/JC_Dropdown";
import { PropertyModel } from "../models/Property";
import { PropertyDefectModel } from "../models/PropertyDefect";
import { DefectImageModel } from "../models/DefectImage";
import { O_AreaModel } from "../models/O_Area";
import { O_LocationModel } from "../models/O_Location";
import { O_OrientationModel } from "../models/O_Orientation";
import { O_SeverityModel } from "../models/O_Severity";
import { DropdownTypeEnum } from "../enums/DropdownType";
import { JC_Utils, JC_Utils_Rooms, JC_Utils_Defects } from "../Utils";
import { JC_FieldOption } from "../models/ComponentModels/JC_FieldOption";
import { JC_ListHeader } from "../components/JC_List/JC_ListHeader";

export default function DefectsPage() {
    // - STATE - //
    const [properties, setProperties] = useState<PropertyModel[]>([]);
    const [selectedProperty, setSelectedProperty] = useState<PropertyModel | null>(null);
    const [selectedRoom, setSelectedRoom] = useState<string>("");
    const [propertyDefects, setPropertyDefects] = useState<PropertyDefectModel[]>([]);
    const [defectImages, setDefectImages] = useState<DefectImageModel[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Option lists for display
    const [areaOptions, setAreaOptions] = useState<O_AreaModel[]>([]);
    const [locationOptions, setLocationOptions] = useState<O_LocationModel[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<O_OrientationModel[]>([]);
    const [severityOptions, setSeverityOptions] = useState<O_SeverityModel[]>([]);

    // Property selector
    const [propertyFieldOptions, setPropertyFieldOptions] = useState<JC_FieldOption[]>([]);
    const [selectedPropertyId, setSelectedPropertyId] = useState<string>("");

    // - EFFECTS - //
    useEffect(() => {
        loadData();
    }, []);

    useEffect(() => {
        if (selectedProperty) {
            loadPropertyDefects(selectedProperty.Id);
        } else {
            setPropertyDefects([]);
            setDefectImages([]);
        }
    }, [selectedProperty]);

    const updatePropertyFieldOptions = useCallback(() => {
        const options = properties.map(property => ({
            OptionId: property.Id,
            Label: property.Address || `Property ${property.Id}`,
            Selected: false
        })).sort((a, b) => a.Label.localeCompare(b.Label));
        setPropertyFieldOptions(options);
    }, [properties]);

    useEffect(() => {
        updatePropertyFieldOptions();
    }, [updatePropertyFieldOptions]);

    // - LOAD DATA - //
    async function loadData() {
        try {
            setIsLoading(true);

            // Load properties and option lists
            const [
                propertiesData,
                areasData,
                locationsData,
                orientationsData,
                severitiesData
            ] = await Promise.all([
                PropertyModel.GetList(),
                O_AreaModel.GetList(),
                O_LocationModel.GetList(),
                O_OrientationModel.GetList(),
                O_SeverityModel.GetList()
            ]);

            setProperties(propertiesData);
            setAreaOptions(areasData);
            setLocationOptions(locationsData);
            setOrientationOptions(orientationsData);
            setSeverityOptions(severitiesData);

        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
        }
    }

    async function loadPropertyDefects(propertyId: string) {
        try {
            setIsLoading(true);

            // Load defects for the property
            const defectsResponse = await fetch(`/api/propertyDefect/byProperty?propertyId=${propertyId}`);
            if (defectsResponse.ok) {
                const defectsData = await defectsResponse.json();
                setPropertyDefects(defectsData.result || []);

                // Load all defect images for this property's defects
                const defectIds = (defectsData.result || []).map((d: PropertyDefectModel) => d.Id);
                if (defectIds.length > 0) {
                    const imagesData = await DefectImageModel.GetList();
                    // Filter images for this property's defects
                    const propertyImages = imagesData.filter((img: DefectImageModel) =>
                        defectIds.includes(img.DefectId)
                    );
                    setDefectImages(propertyImages);
                } else {
                    setDefectImages([]);
                }
            }
        } catch (error) {
            console.error('Error loading property defects:', error);
        } finally {
            setIsLoading(false);
        }
    }



    // - HANDLERS - //
    function handlePropertySelection(propertyId: string) {
        setSelectedPropertyId(propertyId);
        const property = properties.find(p => p.Id === propertyId);
        setSelectedProperty(property || null);
        setSelectedRoom(""); // Reset room selection
    }

    function handleRoomSelection(roomName: string) {
        setSelectedRoom(roomName);
    }

    // - COMPUTED VALUES - //
    const selectedRooms = selectedProperty ?
        JC_Utils_Rooms.parseRoomsJson(selectedProperty.RoomsListJson) : [];

    const selectedRoomNames = selectedProperty ?
        JC_Utils_Rooms.getSelectedRoomNames(selectedRooms, []) : [];

    // For now, show all defects regardless of room selection
    // In a real implementation, you might filter defects by room/area
    const filteredDefects = selectedRoom ?
        JC_Utils_Defects.getDefectsForRoom(selectedRoom, propertyDefects, []) :
        propertyDefects;

    // Helper functions to get option names
    const getAreaName = (code: string) => areaOptions.find(a => a.Code === code)?.Name || code;
    const getLocationName = (code: string) => locationOptions.find(l => l.Code === code)?.Name || code;
    const getOrientationName = (code: string) => orientationOptions.find(o => o.Code === code)?.Name || code;
    const getSeverityName = (code: string) => severityOptions.find(s => s.Code === code)?.Name || code;

    // List headers for defects table
    const defectHeaders: JC_ListHeader[] = [
        { sortKey: "AreaCode", label: "Area" },
        { sortKey: "LocationCode", label: "Location" },
        { sortKey: "OrientationCode", label: "Orientation" },
        { sortKey: "Defects", label: "Defects" },
        { sortKey: "ServerityCode", label: "Severity" },
        { sortKey: "Images", label: "Images" }
    ];

    // - RENDER - //
    return (
        <div className={styles.mainContainer}>
            {/* Property Selector */}
            <div className={styles.propertySelector}>
                <div className={styles.selectorLabel}>Select Property:</div>
                <JC_Dropdown
                    type={DropdownTypeEnum.Default}
                    placeholder="Choose a property"
                    options={propertyFieldOptions}
                    selectedOptionId={selectedPropertyId}
                    onSelection={handlePropertySelection}
                    enableSearch={true}
                />
            </div>

            {/* Left Pane - Rooms List */}
            <div className={styles.leftPane}>
                <div className={styles.leftPaneHeader}>
                    Rooms
                </div>
                <div className={styles.roomsList}>
                    {!selectedProperty ? (
                        <div className={styles.noRooms}>
                            Select a property to view rooms
                        </div>
                    ) : selectedRoomNames.length === 0 ? (
                        <div className={styles.noRooms}>
                            No rooms configured for this property
                        </div>
                    ) : (
                        <>
                            {/* All Rooms option */}
                            <div
                                className={`${styles.roomItem} ${selectedRoom === "" ? styles.selected : ''}`}
                                onClick={() => handleRoomSelection("")}
                            >
                                All Rooms
                            </div>
                            {/* Individual rooms */}
                            {selectedRoomNames.map(roomName => (
                                <div
                                    key={roomName}
                                    className={`${styles.roomItem} ${selectedRoom === roomName ? styles.selected : ''}`}
                                    onClick={() => handleRoomSelection(roomName)}
                                >
                                    {roomName}
                                </div>
                            ))}
                        </>
                    )}
                </div>
            </div>

            {/* Right Pane - Defects List */}
            <div className={styles.rightPane}>
                <div className={styles.rightPaneHeader}>
                    Property Defects
                    {selectedProperty && (
                        <div className={styles.defectCount}>
                            {filteredDefects.length} defect{filteredDefects.length !== 1 ? 's' : ''}
                            {selectedRoom && ` in ${selectedRoom}`}
                        </div>
                    )}
                </div>

                {!selectedProperty ? (
                    <div className={styles.noRoomSelected}>
                        Select a property to view defects
                    </div>
                ) : filteredDefects.length === 0 ? (
                    <div className={styles.noDefects}>
                        {isLoading ? "Loading defects..." : "No defects found"}
                    </div>
                ) : (
                    <div className={styles.defectsContainer}>
                        <div className={styles.defectsTable}>
                            <JC_List
                                overrideClass="defectsListOverride"
                                items={filteredDefects}
                                headers={defectHeaders}
                                defaultSortKey="AreaCode"
                                defaultSortDirection="asc"
                                row={(defect: PropertyDefectModel) => (
                                    <>
                                        <td>{getAreaName(defect.AreaCode)}</td>
                                        <td>{getLocationName(defect.LocationCode)}</td>
                                        <td>{getOrientationName(defect.OrientationCode)}</td>
                                        <td className="defectsColumn">{defect.Defects}</td>
                                        <td>{getSeverityName(defect.ServerityCode)}</td>
                                        <td className="imagesColumn">
                                            {JC_Utils_Defects.countDefectImages(defect.Id, defectImages)}
                                        </td>
                                    </>
                                )}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
