import { JC_Utils } from "../Utils";

export async function JC_Put<T>(routeName:string, newRecord:T, params?:any) {
    const response = await fetch(`/api/${routeName}${params != null ? `?${new URLSearchParams(params)}` : ""}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newRecord)
    });
    if (!response.ok) {
        let responseJson = await response.json();
        throw new Error(!JC_Utils.stringNullOrEmpty(responseJson.error) ? responseJson.error : `Failed to create ${JC_Utils.routeNameToDescription(routeName)}.`);
    }
    return (await response.json()).result;
}